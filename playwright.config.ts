import { defineConfig } from '@playwright/test';
import { Settings } from './support/settings';

export default defineConfig({
  testDir: './tests',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 3,
  workers: process.env.CI ? 1 : 2,
  reporter: [['list'], ['html'], ['allure-playwright']],
  expect: {
    timeout: Settings.timeouts.long,
  },
  timeout: Settings.timeouts.long,

  use: {
    baseURL: Settings.baseUrl,

    trace: 'retain-on-failure',
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
    navigationTimeout: Settings.timeouts.medium,
    actionTimeout: Settings.timeouts.small,
  },

  projects: [
    // mobile chrome
    {
      name: 'chrome-mobile',
      use: {
        browserName: 'chromium',
        viewport: { width: 375, height: 667 },
        isMobile: true,
        hasTouch: true,
      },
    },
    // tablet chrome
    {
      name: 'chrome-tablet',
      use: {
        browserName: 'chromium',
        viewport: { width: 768, height: 1024 },
        isMobile: true,
        hasTouch: true,
      },
    },
    // desktop chrome
    {
      name: 'chrome-desktop',
      use: {
        browserName: 'chromium',
        viewport: { width: 1024, height: 768 },
        isMobile: false,
        hasTouch: false,
      },
    },
    // mobile firefox
    {
      name: 'firefox-mobile',
      use: {
        browserName: 'firefox',
        viewport: { width: 375, height: 667 },
        hasTouch: true,
      },
    },
    // tablet firefox
    {
      name: 'firefox-tablet',
      use: {
        browserName: 'firefox',
        viewport: { width: 768, height: 1024 },
        hasTouch: true,
      },
    },
    // desktop firefox
    {
      name: 'firefox-desktop',
      use: {
        browserName: 'firefox',
        viewport: { width: 1024, height: 768 },
        hasTouch: false,
      },
    },
    // mobile webkit
    {
      name: 'webkit-mobile',
      use: {
        browserName: 'webkit',
        viewport: { width: 375, height: 667 },
        isMobile: true,
        hasTouch: true,
      },
    },
    // tablet webkit
    {
      name: 'webkit-tablet',
      use: {
        browserName: 'webkit',
        viewport: { width: 768, height: 1024 },
        isMobile: true,
        hasTouch: true,
      },
    },
    // desktop webkit
    {
      name: 'webkit-desktop',
      use: {
        browserName: 'webkit',
        viewport: { width: 1024, height: 768 },
        isMobile: false,
        hasTouch: false,
      },
    },
  ],
});
