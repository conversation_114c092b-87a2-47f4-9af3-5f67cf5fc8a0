export enum VerificationTier {
  Welcome = 'welcome',
  Scale = 'scale',
  Unlimited = 'unlimited',
}

export enum TCurrencyAccountSelect {
  USD = 'USD',
  EUR = 'EUR',
  BTC = 'BTC',
  USDT = 'USDT',
}

export type TCurrency =
  | TCurrencyAccountSelect.USD
  | TCurrencyAccountSelect.EUR
  | TCurrencyAccountSelect.BTC
  | TCurrencyAccountSelect.USDT;

export const CurrencyAccountSelectMap: Record<TCurrencyAccountSelect, number> = {
  [TCurrencyAccountSelect.USD]: 0,
  [TCurrencyAccountSelect.EUR]: 1,
  [TCurrencyAccountSelect.BTC]: 2,
  [TCurrencyAccountSelect.USDT]: 3,
};

/*
Example of ExchangeRatesResponse:
const exampleOfExchangeRatesResponse = {
  data: {
    USD: {
      EUR: "0.********",
      BTC: "0.********",
      USDT: "0.********",
    },
    EUR: {
      USD: "1.********",
      BTC: "0.********",
      USDT: "1.********",
    },
    BTC: {
      USD: "114853.********",
      EUR: "98310.********",
      USDT: "114853.********",
    },
    USDT: {
      USD: "0.********",
      EUR: "0.********",
      BTC: "0.********",
    },
  },
}
*/
export type ExchangeRatesResponse = {
  data: {
    [key in TCurrency]: {
      [key in TCurrency]: string;
    };
  };
};

// Subscription types
// Extra small, Small, Medium, Large
export enum SubscriptionTierTitle {
  ExtraSmall = 'Extra Small',
  Small = 'Small',
  Medium = 'Medium',
  Large = 'Large',
}

// Subscription extensions
/**
 * "Pay 2 Go" - 1 card
 * "Ten cards" - 10 cards
 * "Fifty cards" - 50 cards
 * "One hundred cards" - 100 cards
 */
export enum SubscriptionExtensionTitle {
  Pay2Go = 'Pay 2 Go',
  TenCards = 'Ten cards',
  FiftyCards = 'Fifty cards',
  OneHundredCards = 'One hundred cards',
}

export type TExtension =
  | SubscriptionExtensionTitle.Pay2Go
  | SubscriptionExtensionTitle.TenCards
  | SubscriptionExtensionTitle.FiftyCards
  | SubscriptionExtensionTitle.OneHundredCards;

export const ExtensionNumberMap: Record<SubscriptionExtensionTitle, number> = {
  [SubscriptionExtensionTitle.Pay2Go]: 1,
  [SubscriptionExtensionTitle.TenCards]: 10,
  [SubscriptionExtensionTitle.FiftyCards]: 50,
  [SubscriptionExtensionTitle.OneHundredCards]: 100,
};

// Advertisement card types
export enum CardType {
  Advertisement = 'Advertisement',
  Facebook = 'Facebook',
  Google = 'Google',
  TikTok = 'TikTok',
  Ultima = 'Ultima',
}

export type TCardType =
  | CardType.Advertisement
  | CardType.Facebook
  | CardType.Google
  | CardType.TikTok
  | CardType.Ultima;
