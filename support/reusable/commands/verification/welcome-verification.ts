import { CountrySetOne, CountrySetSelection, performCountrySelect } from './country-select';
import { WelcomeVerificationScreen } from '../../../pages/widgets/WelcomeVerificationScreen';
import { Page } from '@playwright/test';

export const performWelcomeVerification = async (
  firstName: string = 'Sam',
  lastName: string = 'Smith',
  birthday: string = '12.12.2001',
  country: CountrySetSelection = CountrySetOne.AL,
  page: Page
) => {
  await performCountrySelect(country, page);
  const welcomeStep = new WelcomeVerificationScreen(page);
  await welcomeStep.fillFirstName(firstName);
  await welcomeStep.fillLastName(lastName);
  await welcomeStep.fillBirthDate(birthday);
  await welcomeStep.clickContinue();
  const confirm = await welcomeStep.clickConfirm();
  if (confirm) {
    await welcomeStep.clickConfirm();
  }
};
