import { Page } from '@playwright/test';

const FRONT_IMAGE_PATH = 'support/files/verification/ok-germany-id-front.png';
const BACK_IMAGE_PATH = 'support/files/verification/ok-germany-id-back.png';

export const performSumsubScaleVerification = async (
  page: Page,
  frontImagePath = FRONT_IMAGE_PATH,
  backImagePath = BACK_IMAGE_PATH
) => {
  // frame
  const contentFrame = page.locator('#sumframe').contentFrame();

  // wait for the continuing button
  const continueButton = contentFrame.getByRole('button', { name: 'Continue' });
  await continueButton.click();

  // start
  const startVerificationButton = contentFrame.getByRole('button', { name: 'Start verification' });
  await startVerificationButton.click();

  // agree
  const agreeButton = contentFrame.getByRole('button', { name: 'Agree and continue' });
  await agreeButton.click();

  // select id card as a method
  await contentFrame.getByText('ID card').click();

  // continue
  await contentFrame.getByRole('button', { name: 'Continue', exact: true }).click();

  // upload front image
  await contentFrame.getByRole('button', { name: 'Front side' }).setInputFiles(frontImagePath);

  // upload back image
  await contentFrame.getByRole('button', { name: 'Back side' }).setInputFiles(backImagePath);

  // continue
  const continueButton2 = contentFrame.getByRole('button', { name: 'Continue' }).first();
  await continueButton2.click();
};
