import { clearNovemberExperiment } from '../../../helpers/page-js-helpers';
import { DashboardPage } from '../../../pages/DashboardPage';
import { RegisterPage } from '../../../pages/RegisterPage';
import { expect, Page, test } from '@playwright/test';

export const performRegistration = async (email: string, password: string, page: Page) => {
  await test.step('Perform registration', async () => {
    const registerPage = new RegisterPage(page);
    await registerPage.goto();
    await registerPage.continueWithEmail();
    await registerPage.fillEmail(email);
    await registerPage.fillPassword(password);
    await registerPage.fillConfirmPassword(password);
    await registerPage.checkAgreements();
    await registerPage.submit();
    await expect(page).toHaveURL(/.*dashboard.*/);
    await clearNovemberExperiment(page);
    return new DashboardPage(page).goto();
  });
};
