import { clearNovemberExperiment } from '../../../helpers/page-js-helpers';
import { waitForNetworkIdle } from '../../../helpers/wait-for-network-idle';
import { DashboardPage } from '../../../pages/DashboardPage';
import { LoginPage } from '../../../pages/LoginPage';
import test, { expect, Page } from '@playwright/test';

export const performLogin = async (email: string, password: string, page: Page) => {
  await test.step('Perform login', async () => {
    const loginPage = new LoginPage(page);
    await loginPage.goto();
    await loginPage.continueWithEmail();
    await loginPage.fillEmail(email);
    await loginPage.fillPassword(password);
    await loginPage.submit();
    await waitForNetworkIdle(page);
    await clearNovemberExperiment(page);
    await new DashboardPage(page).goto();
    await expect(page).toHaveURL(/.*dashboard.*/);
  });
};
