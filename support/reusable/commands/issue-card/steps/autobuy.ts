import { AutoBuyPaymentStep } from '../../../../pages/widgets/issue-card/steps/AutoBuyPaymentStep';
import { paymentCreate } from '../../../../helpers/emulate-api';
import { Page } from '@playwright/test';

export const performAutobuy = async (page: Page) => {
  const autoBuyStep = new AutoBuyPaymentStep(page);
  const address = await autoBuyStep.getWalletAddress();
  const amount = await autoBuyStep.getTotalTopUp();
  await paymentCreate(address, amount);
};
