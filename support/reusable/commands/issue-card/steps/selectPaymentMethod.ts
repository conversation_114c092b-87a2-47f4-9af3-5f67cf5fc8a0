import { CurrencyAccountSelectMap, TCurrencyAccountSelect } from '../../../../types';
import test, { Page } from '@playwright/test';

//issue card: select payment method from dropdown by order
export const selectPaymentMethod = async (
  page: Page,
  account: TCurrencyAccountSelect = TCurrencyAccountSelect.USDT
) => {
  await test.step(`Select payment method: ${account}`, async () => {
    const selectedOptionId = 'accounts-and-cards-select-selected-option';
    const index = CurrencyAccountSelectMap[account];
    // const openButton = page.getByTestId('ui-select').describe('Account select open button');
    const optionId = 'accounts-and-cards-select-option-account';

    await page.getByTestId(selectedOptionId).click();
    await page.getByTestId(optionId).nth(index).describe('Account select option').click();
  });
};
