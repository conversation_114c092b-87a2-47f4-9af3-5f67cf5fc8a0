import { Page } from '@playwright/test';
import { SubscriptionExtensionTitle, TExtension } from '../../../types';
import { UserTariffExtensionTabsSelect } from '../../../pages/widgets/ui/userTariffExtensionTabsSelect';
import { SubscriptionDetailPage } from '../../../pages/subscribtion/SubscriptionDetailPage';

export const performBuySubscriptionExtension = async (
  page: Page,
  extension: TExtension = SubscriptionExtensionTitle.TenCards
) => {
  const subscriptionDetailPage = new SubscriptionDetailPage(page);
  await subscriptionDetailPage.goto();
  await subscriptionDetailPage.manageSubscriptionButton.click();
  await subscriptionDetailPage.privateManagementModal.raiseLimitButtonNoExtensions.click();

  const tabsSelect = new UserTariffExtensionTabsSelect(page);
  await tabsSelect.getTabByName(extension).click();
  await page.getByTestId('tariff-extension-continue-btn').click();
  await page.getByTestId('tariff-extension-confirm-btn').click();
  await page.getByTestId('tariff-extension-later-btn').click();
};
