import { CreateCardSummary } from '../../../pages/widgets/issue-card/CreateCardSummary';
import { getExchangeRates } from '../../../helpers/page-js-helpers';
import { TCurrency, TCurrencyAccountSelect } from '../../../types';
import { expect, Page } from '@playwright/test';
import { test } from 'playwright/test';

// verifying total calculation in the summary widget is correct
export const haveCorrectTotalCalculated = async (
  page: Page,
  selectedAccount: TCurrency = TCurrencyAccountSelect.USD
) => {
  await test.step('Verify total for currency: ' + selectedAccount, async () => {
    const createCardSummary = new CreateCardSummary(page);

    const monthlyPayment = await createCardSummary.getMonthlyPaymentValue();
    const startingBalance = await createCardSummary.getStartingBalance();
    const topUpFee = await createCardSummary.getTopUpFee();
    const displayedTotal = await createCardSummary.getTotal();

    expect(monthlyPayment).toBeTruthy();
    expect(startingBalance).toBeTruthy();
    expect(displayedTotal).toBeTruthy();

    const apiExchangeRate = await getExchangeRates(page, selectedAccount);

    const commission = (startingBalance * topUpFee) / (1 - topUpFee);
    const expectedTotal = (commission + monthlyPayment + startingBalance) / apiExchangeRate;

    expect(Math.abs(displayedTotal - expectedTotal)).toBeLessThan(0.01);
  });
};
