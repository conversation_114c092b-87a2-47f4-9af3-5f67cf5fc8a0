import { Page } from '@playwright/test';
import { DashboardPage } from '../DashboardPage';
import { PrivateManagementModal } from '../widgets/subscription/privateManagementModal';
import { extractNumberFromString } from '../../helpers/extract-number-from-string';

export class SubscriptionDetailPage extends DashboardPage {
  path = '/app/subscription/detail';

  currentTariffBlock = this.page.getByTestId('current-plan-block').describe('Current plan block');

  currentTariffTitle = this.currentTariffBlock
    .getByTestId('private-block-content')
    .describe('Current user tariff title');

  currentTariffText = this.currentTariffBlock
    .getByTestId('private-block-text')
    .describe('Current user tariff text');

  manageSubscriptionButton = this.page
    .getByTestId('manage-subscription-btn')
    .describe('Manage button');

  // upgrade
  upgradeTariffButton = this.page
    .getByTestId('upgrade-tariff-btn')
    .describe('Upgrade tariff button');

  upgradeConnectButton = this.page
    .getByTestId('upgrade-subscription-btn')
    .describe('Connect private tariff button');

  modalSuccessButton = this.page
    .getByTestId('modal-success-btn')
    .describe('Close: Success modal button');

  upgradePlanPanelButton = this.page
    .getByTestId('pst-private.upgrade-plan-panel')
    .locator('button')
    .describe('Upgrade plan button');

  constructor(page: Page) {
    super(page);
  }

  get privateManagementModal() {
    return new PrivateManagementModal(this.page);
  }

  async getCurrentTariffTitle() {
    const text =
      (await this.currentTariffBlock.getByTestId('private-block-content').textContent()) ?? '';
    return text.trim();
  }

  async getTotalAvailableCards() {
    const text = await this.currentTariffText.textContent();
    return extractNumberFromString(text);
  }
}
