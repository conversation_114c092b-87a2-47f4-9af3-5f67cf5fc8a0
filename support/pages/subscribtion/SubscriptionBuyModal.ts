import { Page } from '@playwright/test';
import { Widget } from '../widgets/Widget';

export class SubscriptionBuyModal extends Widget {
  topUpAccountBalanceBtn = this.page.getByTestId('top-up-account-btn');

  totalAmountRow = this.page.getByTestId('total-amount-row > p').nth(1);

  accountSelectedOption = this.page.getByTestId('accounts-and-cards-select-selected-option');

  connectTariffBtn = this.page
    .getByTestId('connect-account-btn')
    .describe('Connect private tariff button');
  purchaseCompleteButton = this.page
    .getByTestId('purchase-complete-button')
    .describe('Complete purchase button');
  purchaseCompleteTakeLaterButton = this.page
    .getByTestId('purchase-complete-take-later-button')
    .describe('Pick up later button');

  purchaseCompletePickCardNowButton = this.page
    .getByTestId('purchase-complete-take-now-button')
    .describe('Pick up a card');

  constructor(page: Page) {
    super(page);
  }

  async getTotalAmount() {
    const text = await this.totalAmountRow.textContent();
    const number = text?.replace(/[^0-9.]/g, '');
    return parseFloat(number ?? '');
  }
}
