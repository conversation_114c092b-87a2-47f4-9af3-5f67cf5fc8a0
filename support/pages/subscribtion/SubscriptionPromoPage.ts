import { Page } from '@playwright/test';
import { DashboardPage } from '../DashboardPage';
import { SubscriptionBuyModal } from './SubscriptionBuyModal';
import { SubscriptionCards } from '../widgets/subscription/subscriptionCards';

export class SubscriptionPromoPage extends DashboardPage {
  path = '/app/subscription/promo';

  constructor(page: Page) {
    super(page);
  }

  get subscriptionBuyModal() {
    return new SubscriptionBuyModal(this.page);
  }

  get subscriptionCards() {
    return new SubscriptionCards(this.page);
  }
}
