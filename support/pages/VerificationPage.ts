import { AvailableVerificationWidget } from './widgets/AvailableVerificationWidget';
import { Page } from '@playwright/test';
import { BasePage } from './BasePage';

export class VerificationPage extends BasePage {
  path = '/app/settings/verification';

  verificationApproved = this.page.getByTestId('verification-approved');

  countrySelectContinueButton = this.page.getByTestId('country-select-continue-button');

  constructor(page: Page) {
    super(page);
  }

  get availableVerificationScreen() {
    return new AvailableVerificationWidget(this.page);
  }
}
