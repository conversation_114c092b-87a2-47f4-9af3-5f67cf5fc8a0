import { Page } from '@playwright/test';
import { Widget } from './Widget';

export class VerificationButtonWidget extends Widget {
  static VERIFICATION_WIDGET_TEXT_ID = 'verification-widget-text';
  static VERIFICATION_BUTTON_ID = 'verification-widget';

  verificationButton = this.page
    .getByTestId(VerificationButtonWidget.VERIFICATION_BUTTON_ID)
    .describe('Verification Button');

  constructor(page: Page) {
    super(page);
    this.verificationButton = this.page
      .getByTestId(VerificationButtonWidget.VERIFICATION_BUTTON_ID)
      .describe('Verification Button');
  }
}
