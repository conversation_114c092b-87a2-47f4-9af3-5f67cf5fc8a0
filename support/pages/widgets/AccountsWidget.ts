import { TCurrency, TCurrencyAccountSelect } from '../../types';
import { Page } from '@playwright/test';
import { Widget } from './Widget';

export class AccountsWidget extends Widget {
  static USDT_ACCOUNT_ID = 'usdt_account';
  static BTC_ACCOUNT_ID = 'btc_account';
  static USD_ACCOUNT_ID = 'usd_account';
  static EUR_ACCOUNT_ID = 'eur_account';

  static ACCOUNT_BALANCE_ID = 'account-balance';

  usdtAccount = this.page.getByTestId(AccountsWidget.USDT_ACCOUNT_ID).describe('USDT account card');

  constructor(page: Page) {
    super(page);
  }

  async getAccountByCurrency(currency: TCurrency) {
    let accountId: string;
    switch (currency) {
      case TCurrencyAccountSelect.USDT:
        accountId = AccountsWidget.USDT_ACCOUNT_ID;
        break;
      case TCurrencyAccountSelect.BTC:
        accountId = AccountsWidget.BTC_ACCOUNT_ID;
        break;
      case TCurrencyAccountSelect.USD:
        accountId = AccountsWidget.USD_ACCOUNT_ID;
        break;
      case TCurrencyAccountSelect.EUR:
        accountId = AccountsWidget.EUR_ACCOUNT_ID;
        break;
    }

    const accountContainer = this.page.getByTestId(accountId).describe(`${currency} account card`);
    await accountContainer.waitFor({ state: 'visible' });
    return accountContainer;
  }

  async getAccountBalanceInUSD(currency: TCurrency) {
    const accountContainer = await this.getAccountByCurrency(currency);
    const balanceContainer = accountContainer
      .getByTestId(AccountsWidget.ACCOUNT_BALANCE_ID)
      .describe('Account balance');
    await balanceContainer.waitFor({ state: 'visible' });
    const balanceText = (await balanceContainer.textContent()) ?? '';
    // $123.45 -> 123.45, ₮123.45 -> 123.45
    return parseFloat(balanceText.replace(/[^0-9.]/g, ''));
  }

  async clickTopUpButton() {
    await this.page.getByRole('button', { name: 'topup-button' }).click();
  }
}
