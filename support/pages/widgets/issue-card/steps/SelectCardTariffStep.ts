import { Page } from '@playwright/test';
import { Widget } from '../../Widget';
import { TCardType } from '../../../../types';

export class SelectCardTariffStep extends Widget {
  static tariffItem = 'issue-card.select-tariff-item';

  static tariffItemTitle = 'issue-card.select-tariff-item-title';

  static getCardButton = 'issue-card.select-tariff-item-get-card-button';

  selectTariffContainer = this.page.getByTestId('issue-card.select-tariff');

  advertisementCardsContainer = this.page.getByTestId(
    'issue-card.select-tariff-advertisement-list'
  );

  constructor(page: Page) {
    super(page);
  }

  async selectTariff(title: TCardType) {
    await this.getCardButtonByTitle(title).click();
  }

  getCardButtonByTitle = (title: TCardType) => {
    return this.getTariffItemByTitle(title).getByTestId(SelectCardTariffStep.getCardButton);
  };

  getTariffItemByTitle = (title: TCardType) => {
    return this.page.getByTestId(SelectCardTariffStep.tariffItem).filter({
      has: this.page.getByTestId(SelectCardTariffStep.tariffItemTitle).filter({ hasText: title }),
    });
  };
}
