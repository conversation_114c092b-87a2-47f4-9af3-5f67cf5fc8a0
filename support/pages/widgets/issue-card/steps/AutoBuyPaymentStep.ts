import { Page } from '@playwright/test';
import { Widget } from '../../Widget';

export class AutoBuyPaymentStep extends Widget {
  walletAddressContainer = this.page
    .getByTestId('selected-account-address')
    .describe('Wallet address');

  summaryTotalContainer = this.page
    .getByTestId('create-card-summary-total')
    .describe('Summary total');

  constructor(page: Page) {
    super(page);
  }

  async getTotalTopUp() {
    const totalWithCurrency =
      (await this.summaryTotalContainer.locator('> p').nth(1).textContent()) ?? '';

    // ₮61.97 -> 61.97, $61.97 -> 61.97
    return totalWithCurrency.replace(/[^0-9.]/g, '');
  }

  async getWalletAddress(): Promise<string> {
    return (await this.walletAddressContainer.textContent()) ?? '';
  }
}
