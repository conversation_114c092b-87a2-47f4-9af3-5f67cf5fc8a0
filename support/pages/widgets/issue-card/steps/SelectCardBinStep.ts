import { Page } from '@playwright/test';
import { Widget } from '../../Widget';

export class SelectCardBinStep extends Widget {
  static ID = 'issue-card.select-bin-step';
  static BIN_TABLE_ITEM_BUTTON_ID = 'select-bin-table-item-button';
  static BIN_TABLE_ITEM_BUTTON_MOBILE_ID = 'select-bin-table-item-button-mobile';

  container = this.page.getByTestId(SelectCardBinStep.ID);

  binOrderButtonsAll = this.page
    .getByTestId(SelectCardBinStep.BIN_TABLE_ITEM_BUTTON_ID)
    .or(this.page.getByTestId(SelectCardBinStep.BIN_TABLE_ITEM_BUTTON_MOBILE_ID))
    .describe('All BIN order buttons3');

  constructor(page: Page) {
    super(page);
  }

  // select the bin table item by index
  async getBinTableItemButtonByIndex(index: number = 0) {
    return this.binOrderButtonsAll.nth(index).describe('Bin Issue button');
  }

  async selectBinByIndex(index: number) {
    await this.waitForTableToBeVisible();
    await (await this.getBinTableItemButtonByIndex(index)).click();
  }

  async waitForTableToBeVisible() {
    await this.binOrderButtonsAll.first().waitFor({ state: 'visible' });
  }
}
