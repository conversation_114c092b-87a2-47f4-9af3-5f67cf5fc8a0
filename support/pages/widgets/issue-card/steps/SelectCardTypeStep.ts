import { Page } from '@playwright/test';
import { Widget } from '../../Widget';

export class SelectCardTypeStep extends Widget {
  static CONTAINER_ID = 'issue-card.select-tariff';
  static ADVERTISE_CARD_BUTTON_ID = 'issue-advertise-card-button';
  static ULTIMA_CARD_BUTTON_ID = 'issue-ultima-card-button';

  issueAdvertiseCardButton = this.page
    .getByTestId(SelectCardTypeStep.ADVERTISE_CARD_BUTTON_ID)
    .describe('Adv: More details button');

  issueUltimaCardButton = this.page
    .getByTestId(SelectCardTypeStep.ULTIMA_CARD_BUTTON_ID)
    .describe('Ultima: Issue a card button');

  container = this.page.getByTestId(SelectCardTypeStep.CONTAINER_ID);  

  constructor(page: Page) {
    super(page);
  }

  async selectCardType(type: 'advertisement' | 'ultima' = 'advertisement') {
    if (type === 'advertisement') {
      await this.issueAdvertiseCardButton.click();
    } else {
      await this.issueUltimaCardButton.click();
    }
  }
}
