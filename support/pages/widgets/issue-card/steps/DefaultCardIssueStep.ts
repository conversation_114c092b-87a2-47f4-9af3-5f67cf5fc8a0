import { Page } from '@playwright/test';
import { Widget } from '../../Widget';
import { Promocode } from '../Promocode';
import { CreateCardSummary } from '../CreateCardSummary';

export class DefaultCardIssueStep extends Widget {
  startBalanceContainer = this.page.getByTestId('issue-card.start-balance-section');

  startBalanceButtons = this.page
    .getByTestId('issue-card.start-balance-button')
    .describe('Start balance buttons');

  startBalanceInput = this.startBalanceContainer
    .locator('input[type="text"]')
    .describe('Start balance input');

  submitButton = this.page.locator('[data-cy="order_button"]').describe('Submit button');

  // terms and conditions checkbox
  termsAndConditionsCheckbox = this.page
    .locator('[data-cy="agreement"] > span')
    .describe('Terms and conditions checkbox');

  termsAndConditionsText = this.page
    .getByTestId('card-issue.agreement-text')
    .describe('Terms and conditions text');

  get summary() {
    return new CreateCardSummary(this.page);
  }

  get promocode() {
    return new Promocode(this.page);
  }

  constructor(page: Page) {
    super(page);
  }

  async clickStartBalance(index: number) {
    await this.startBalanceButtons.nth(index).click();
  }

  async checkTermsAndConditions() {
    await this.termsAndConditionsCheckbox.click();
  }

  async clickSubmit() {
    await this.submitButton.click();
  }
}
