import { Page } from '@playwright/test';
import { Widget } from '../Widget';

export class Promocode extends Widget {
  static ID = 'issue-card.promo-code';

  widget = this.page.getByTestId(Promocode.ID);

  promocodeButton = this.widget
    .getByTestId('issue-card.promo-code-button')
    .describe('I have promocode button');

  promocodeInput = this.widget
    .getByTestId('issue-card.promo-code-input')
    .locator('input')
    .describe('Promocode input field');

  promocodeApplyButton = this.widget
    .getByTestId('issue-card.promo-code-apply-button')
    .describe('Promocode apply button');

  promocodeListItem = this.widget
    .getByTestId('issue-card.promo-code-list-item')
    .describe('Applied promocode item');

  promocodeToggle = this.widget
    .getByTestId('issue-card.promo-code-toggle')
    .describe('Promocode toggle');

  constructor(page: Page) {
    super(page);
  }

  async applyPromocode(code: string) {
    await this.promocodeButton.click();
    await this.promocodeInput.fill(code);
    await this.promocodeApplyButton.click();
  }
}
