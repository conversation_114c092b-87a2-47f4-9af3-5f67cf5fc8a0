import { Page } from '@playwright/test';
import { Widget } from '../Widget';

export class CreateCardSummary extends Widget {
  static ID = 'create-card-summary';

  widget = this.page.getByTestId(CreateCardSummary.ID);

  monthlyPaymentValue = this.widget.getByTestId('create-card-summary-period');

  startingBalanceContainer = this.widget.getByTestId('create-card-summary-start-balance');

  totalContainer = this.widget.getByTestId('create-card-summary-total');

  exchangeRateValue = this.widget.getByTestId('exchange-rate-value');

  topUpFee = this.widget.getByTestId('create-card-summary-top-up-fee');

  // discount
  monthlyPaymentDiscountElement = this.widget
    .getByTestId('issue-card.summary.payment-discount-text')
    .describe('Monthly payment discount text');

  startingBalanceBonusElement = this.widget
    .getByTestId('issue-card.summary.starting-balance-bonus-text')
    .describe('Starting balance bonus text');

  constructor(page: Page) {
    super(page);
  }

  async getMonthlyPaymentValue() {
    const value = await this.monthlyPaymentValue.textContent();
    return value ? parseFloat(value.replace(/[^0-9.]/g, '')) : 0;
  }

  async getStartingBalance() {
    const value = await this.startingBalanceContainer.textContent();
    return value ? parseFloat(value.replace(/[^0-9.]/g, '')) : 0;
  }

  async getTopUpFee() {
    const value = await this.topUpFee.locator('> div').last().textContent();

    const match = value?.match(/([0-9.]+)%/);
    return match ? parseFloat(match[1]) / 100 : 0;
  }

  async getTotal() {
    const value = await this.totalContainer.textContent();
    return value ? parseFloat(value.replace(/[^0-9.]/g, '')) : 0;
  }

  async getExchangeRate(): Promise<number> {
    if (!(await this.exchangeRateValue.isVisible())) {
      return 1; // No exchange rate element means no conversion needed
    }
    const value = await this.exchangeRateValue.textContent();

    const match = value?.match(/=\s*\$?([0-9.]+)/);
    return match ? parseFloat(match[1]) : 0;
  }
}
