import { Page } from '@playwright/test';
import { Widget } from '../Widget';
import { TExtension } from '../../../types';

export class UserTariffExtensionTabsSelect extends Widget {
  static ID = 'tariff-extension-tabs-select';
  static TITLE_ID = 'tariff-extension-name';

  static PURCHASE_BUTTON_ID = 'subscription.up-limit-process-purchase-btn'
  static ISSUE_CARD_NOW_BUTTON_ID = 'tariff-extension-issue-card-btn';
  static DONE_BUTTON_ID = 'tariff-extension-done-btn';

  widget = this.page.getByTestId(UserTariffExtensionTabsSelect.ID).describe('Tariff extension tabs select');
  tabs = this.page.getByTestId('tariff-extension-tab').describe('Tariff extension tabs');

  constructor(page: Page) {
    super(page);
  }

  getTabByName(tabName: TExtension) {
    return this.tabs
      .filter({
        has: this.page
          .getByTestId(UserTariffExtensionTabsSelect.TITLE_ID)
          .getByText(tabName, { exact: true }),
      })
      .describe(`Tab with name ${tabName}`);
  }
}
