import { Page } from '@playwright/test';
import { Widget } from '../Widget';

export class AccountSelect extends Widget {
  static ID = 'accounts-and-cards-select';

  widget = this.page.getByTestId(AccountSelect.ID);

  selectedOption = this.widget.getByTestId('ui-select').describe('Selected option');

  selectedOptionBalance = this.widget
    .getByTestId('account-balance')
    .describe('Selected option balance');

  options = this.widget
    .getByTestId('accounts-and-cards-select-option-account')
    .describe('Account select options');

  error = this.widget.getByTestId('ui-select-error').describe('Select error message');

  constructor(page: Page) {
    super(page);
  }
}
