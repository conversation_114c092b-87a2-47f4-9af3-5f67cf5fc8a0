import { Page } from "@playwright/test";
import { Widget } from "../Widget";

export class CardLimitDialog extends Widget {
  static ID = 'subscription.card-limit-modal';

  widget = this.page.getByTestId(CardLimitDialog.ID).describe('Card Limit Dialog');

  // subscription.raise-limit-button
  raiseLimitButton = this.widget.getByTestId('subscription.raise-limit-button').describe(
    'Raise card limits button'
  );

  constructor(page: Page) {
    super(page);
  }
}

  