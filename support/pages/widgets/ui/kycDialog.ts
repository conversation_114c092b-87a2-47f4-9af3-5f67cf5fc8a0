import { Widget } from "../Widget";
import { Page } from "@playwright/test";

export class KYCDialog extends Widget {
  static ID = 'ui-modal.root';

  widget = this.page.getByTestId(KYCDialog.ID).describe('KYC Dialog');

  title = this.widget.getByTestId('ui-modal.title').describe('KYC Dialog Title');

  cancelButton = this.widget.getByTestId('ui-dialog.cancel-btn').describe('Cancel Button');

  confirmButton = this.widget.getByTestId('ui-dialog.confirm-btn').describe('Confirm <PERSON><PERSON>');

  constructor(page: Page) {
    super(page);
  }
}
