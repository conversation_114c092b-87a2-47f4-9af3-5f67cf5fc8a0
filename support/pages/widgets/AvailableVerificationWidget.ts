import { Page } from '@playwright/test';
import { Widget } from './Widget';

export class AvailableVerificationWidget extends Widget {
  static VERIFICATION_ITEM_UPGRADE_BUTTON_ID = 'upgrade-verification-ready-button';
  static AVAILABLE_VERIFICATION_ITEM_ID = 'available-verification-item';
  static VERIFICATION_ITEM_CURRENTLY_VERIFIED_ID = 'verification-item-currently-verified';
  static VERIFICATION_ITEM_UNAVAILABLE_BUTTON_ID = 'verification-item-unavailable-text';
  static VERIFICATION_ITEM_TITLE_ID = 'verification-item-title';

  constructor(page: Page) {
    super(page);
  }

  async clickUpgradeVerification() {
    const button = this.page.getByTestId(
      AvailableVerificationWidget.VERIFICATION_ITEM_UPGRADE_BUTTON_ID
    );
    await button.click();
  }

  async getCurrentlyVerifiedVerificationItem() {
    const verifiedIcon = this.page.getByTestId(
      AvailableVerificationWidget.VERIFICATION_ITEM_CURRENTLY_VERIFIED_ID
    );
    const verifiedItem = this.page
      .getByTestId(AvailableVerificationWidget.AVAILABLE_VERIFICATION_ITEM_ID)
      .filter({ has: verifiedIcon })
      .first();

    return verifiedItem.getByTestId(AvailableVerificationWidget.VERIFICATION_ITEM_TITLE_ID);
  }

  async getCurrentVerificationName() {
    const title = await this.getCurrentlyVerifiedVerificationItem();
    return title.innerText();
  }
}
