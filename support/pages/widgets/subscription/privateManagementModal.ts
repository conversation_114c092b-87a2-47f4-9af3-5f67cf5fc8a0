import { Page } from '@playwright/test';
import { Widget } from '../Widget';

export class PrivateManagementModal extends Widget {
  upgradeButton = this.page.getByTestId('upgrade-tariff-btn');

  currentPlanBlock = this.page
    .getByTestId('current-plan-subscription-block')
    .describe('Current plan block');

  extensionsBlock = this.page
    .getByTestId('extensions-subscription-block')
    .describe('Extensions block');

  // cancel
  dotsVerticalButton = this.currentPlanBlock.getByTestId('dots-vertical-icon');

  cancelSubscriptionButton = this.page.getByTestId('cancel-subscription-btn');

  cancelSubscriptionButtonLosePrivate = this.page.getByTestId(
    'cancel-subscription-button-lose-private'
  );

  cancelSubscriptionButtonAnyWay = this.page.getByTestId('cancel-subscription-anyway-button');

  raiseLimitButton = this.page
    .getByTestId('raise-limit-btn')
    .describe('Raise the limit button: Has extensions');

  raiseLimitButtonNoExtensions = this.page
    .getByTestId('raise-limit-btn-no-extensions')
    .describe('Raise the limit button: No extensions');

  // extensions
  extensionCards = this.extensionsBlock
    .getByTestId('manage-extension-block-card')
    .describe('Extension cards');

  constructor(page: Page) {
    super(page);
  }

  async cancelByReason(reasonIndex = 0) {
    const options = this.page.getByTestId('subscription-cancel-option').describe('Reason options');
    const reasonLabel = options.nth(reasonIndex).locator('label');
    await reasonLabel.waitFor({ state: 'visible' });
    await reasonLabel.click();
    const submitButton = this.page.getByTestId('submit-feedback-button');
    await submitButton.click();
    const doneButton = this.page.getByTestId('subscription-cancel-done-button');
    await doneButton.click();
  }
}
