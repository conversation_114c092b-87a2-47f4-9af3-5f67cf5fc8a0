import { Widget } from '../Widget';
import { SubscriptionTierTitle } from '../../../types';
import { Page } from '@playwright/test';

export class SubscriptionCards extends Widget {
  static SUBSCRIPTION_TARIFF_CARD_ID = 'subscriptions-tariff-card';
  static TARIFF_NAME_ID = 'tariff-name';
  static SELECT_TARIFF_BUTTON_ID = 'select-tariff-button';

  constructor(page: Page) {
    super(page);
  }

  getTariffCards() {
    return this.page.getByTestId(SubscriptionCards.SUBSCRIPTION_TARIFF_CARD_ID);
  }

  async getTariffCardByName(tariffName: SubscriptionTierTitle = SubscriptionTierTitle.ExtraSmall) {
    return this.getTariffCards()
      .filter({
        has: this.page
          .getByTestId(SubscriptionCards.TARIFF_NAME_ID)
          .getByText(tariffName, { exact: true }),
      })
      .describe(`tariff card with name ${tariffName}`);
  }

  async selectTariffByName(tariffName: SubscriptionTierTitle) {
    const card = await this.getTariffCardByName(tariffName);
    const button = card
      .getByTestId(SubscriptionCards.SELECT_TARIFF_BUTTON_ID)
      .describe(`Select ${tariffName} tariff button`);
    await button.click();
  }
}
