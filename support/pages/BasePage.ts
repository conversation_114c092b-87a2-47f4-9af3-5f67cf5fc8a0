import { Page } from '@playwright/test';

export class BasePage {
  page: Page;
  path = '/';

  constructor(page: Page) {
    this.page = page;
  }

  async goto() {
    await this.page.goto(this.path);
  }

  async closeSidebar() {
    const closeButton = this.page
      .getByTestId('close-sidebar-button')
      .describe('Close sidebar button');
    await closeButton.click();
    await closeButton.waitFor({ state: 'hidden' });
  }
}
