import { Page, test } from '@playwright/test';

/**
 * Waits for the network to remain idle for a specified duration.
 * The function watches requests and resolves once there has been no network activity for the specified timeout period.
 *
 * @param {Page} page - The page instance to monitor for network activity.
 * @param {number} [timeout=3000] - The duration, in milliseconds, to wait with no network activity. Defaults to 3000ms.
 * @param {number} [checkInterval=300] - The interval, in milliseconds, at which to check for network idleness. Defaults to 300ms.
 * @returns {Promise<void>} A promise that resolves when the network is idle for the specified timeout duration.
 */
export const waitForNetworkIdle = async (
  page: Page,
  timeout: number = 3000,
  checkInterval: number = 300
): Promise<void> => {
  return test.step(`Wait for network idle (${timeout}ms)`, async () => {
    let lastActivity = Date.now();

    const updateLastActivity = () => {
      lastActivity = Date.now();
    };

    page.on('request', updateLastActivity);
    page.on('requestfinished', updateLastActivity);
    page.on('requestfailed', updateLastActivity);

    return new Promise<void>((resolve) => {
      const interval = setInterval(() => {
        if (Date.now() - lastActivity > timeout) {
          clearInterval(interval);
          resolve();
        }
      }, checkInterval);
    });
  });
};
