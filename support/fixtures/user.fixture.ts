import { expect, Page, test as base } from '@playwright/test';
import { apiUserLogin, apiUserRegister, apiUserUSDTAddress } from '../helpers/backend-api';
import { createUserEmulate, paymentCreate } from '../helpers/emulate-api';
import { clearNovemberExperiment, setUserToken } from '../helpers/page-js-helpers';
import { randomEmail } from '../helpers/random-helpers';
import { DashboardPage } from '../pages/DashboardPage';
import { performSubscription } from '../reusable/commands/subscription/subscription';
import { SubscriptionTierTitle, VerificationTier } from '../types';

export const test = base.extend<{
  loggedInUser: {
    page: Page;
    user: {
      email: string;
      password: string;
    };
  };
  verifiedUser: (verification: VerificationTier) => Promise<{ page: Page; email: string }>;
  positiveUser: (verification: VerificationTier) => Promise<{ page: Page; email: string }>;
  withSubscription: (
    subscription: SubscriptionTierTitle,
    verification?: VerificationTier
  ) => Promise<{ page: Page; email: string }>;
}>({
  loggedInUser: async ({ page }, use) => {
    const newUser = randomEmail();
    const registerResponse = await apiUserRegister(newUser, newUser);
    const token = registerResponse?.data?.token;
    await page.goto('/');
    await setUserToken(page, token);
    await clearNovemberExperiment(page);
    await new DashboardPage(page).goto();
    await use({ page, user: { email: newUser, password: newUser } });
  },
  verifiedUser: async ({ page }, use) => {
    const createVerifiedUser = async (verification: VerificationTier) => {
      const newUser = randomEmail();
      await createUserEmulate({
        user: {
          email: newUser,
          password: newUser,
        },
        verification: { slug: verification },
      });

      const { token } = await apiUserLogin(newUser, newUser);
      await page.goto('/');
      await setUserToken(page, token);
      await clearNovemberExperiment(page);
      await new DashboardPage(page).goto();

      return { page, email: newUser };
    };

    await use(createVerifiedUser);
  },
  positiveUser: async ({ page }, use) => {
    const createPositiveUser = async (verification: VerificationTier = VerificationTier.Scale) => {
      const newUser = randomEmail();
      await createUserEmulate({
        user: {
          email: newUser,
          password: newUser,
        },
        verification: { slug: verification },
      });

      const { token } = await apiUserLogin(newUser, newUser);
      await page.goto('/');
      await setUserToken(page, token);

      const dashboardPage = new DashboardPage(page);
      await dashboardPage.goto();

      const address = await apiUserUSDTAddress(token);
      await paymentCreate(address);

      await new DashboardPage(page).goto();

      return { page, email: newUser };
    };

    await use(createPositiveUser);
  },
  withSubscription: async ({ positiveUser }, use) => {
    const createUserWithSubscription = async (
      subscription: SubscriptionTierTitle = SubscriptionTierTitle.ExtraSmall,
      verification: VerificationTier = VerificationTier.Scale
    ) => {
      const { page: userPage, email } = await positiveUser(verification);
      await performSubscription(userPage, subscription);
      return { page: userPage, email };
    };
    await use(createUserWithSubscription);
  },
});

export { expect };
