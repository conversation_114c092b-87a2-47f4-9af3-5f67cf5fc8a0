import { SubscriptionExtensionTitle } from './../../support/types';
import { selectPaymentMethod } from './../../support/reusable/commands/issue-card/steps/selectPaymentMethod';
import { performWelcomeVerification } from '../../support/reusable/commands/verification/welcome-verification';
import { SelectCardTariffStep } from '../../support/pages/widgets/issue-card/steps/SelectCardTariffStep';
import { DefaultCardIssueStep } from '../../support/pages/widgets/issue-card/steps/DefaultCardIssueStep';
import { SelectCardTypeStep } from '../../support/pages/widgets/issue-card/steps/SelectCardTypeStep';
import { SelectCardBinStep } from '../../support/pages/widgets/issue-card/steps/SelectCardBinStep';
import { SubscriptionCards } from '../../support/pages/widgets/subscription/subscriptionCards';
import { CardApproveStep } from '../../support/pages/widgets/issue-card/steps/CardApproveStep';
import { SubscriptionBuyModal } from '../../support/pages/subscribtion/SubscriptionBuyModal';
import {
  CountrySetOne,
  performCountrySelect,
} from '../../support/reusable/commands/verification/country-select';
import { performRegistration } from '../../support/reusable/commands/auth/registration';
import { SubscriptionTierTitle, TCurrencyAccountSelect } from '../../support/types';
import { waitForNetworkIdle } from '../../support/helpers/wait-for-network-idle';
import { CreateCardPage } from '../../support/pages/CreateCardPage';
import { randomEmail, testUser } from '../../support/helpers/random-helpers';
import { AccountSelect } from '../../support/pages/widgets/ui/accountSelect';
import { closeTopModal } from '../../support/reusable/commands/close-modal';
import { DepositModal } from '../../support/pages/widgets/DepositModal';
import { expect, test } from '../../support/fixtures/user.fixture';
import { paymentCreate } from '../../support/helpers/emulate-api';
import { DashboardPage } from '../../support/pages/DashboardPage';
import { Settings } from '../../support/settings';
import { CardType } from '../../support/types';
import { KYCDialog } from '../../support/pages/widgets/ui/kycDialog';
import { VerificationPage } from '../../support/pages/VerificationPage';
import { performSumsubScaleVerification } from '../../support/reusable/commands/verification/sumsub-scale-verification';
import { SumsubVerificationScreen } from '../../support/pages/widgets/SumsubVerificationScreen';
import { CardLimitDialog } from '../../support/pages/widgets/ui/cardLimitDialog';
import { getUserToken } from '../../support/helpers/page-js-helpers';
import { apiUserUSDTAddress } from '../../support/helpers/backend-api';
import { UserTariffExtensionTabsSelect } from '../../support/pages/widgets/ui/userTariffExtensionTabsSelect';
import { SubscriptionDetailPage } from '../../support/pages/subscribtion/SubscriptionDetailPage';

test(
  'Issue Advertisement card by user with subscription',
  { tag: ['@issue-card', '@advertisement', '@smoke'] },
  async ({ page }) => {
    test.setTimeout(Settings.timeouts.fiveMinutes);
    page.setDefaultTimeout(Settings.timeouts.medium);

    await test.step('Register', async () => {
      const email = randomEmail();
      await performRegistration(email, email, page);

      await expect(page).toHaveURL(/.*dashboard.*/);
      await waitForNetworkIdle(page);
    });

    await test.step('User selects advertisement card on dashboard', async () => {
      await new SelectCardTypeStep(page).selectCardType('advertisement');
      await expect(page).toHaveURL(/.*subscription\/view-tariffs.*/);
    });

    // buys subscription before issuing card
    await test.step('Buy Extra Small subscription', async () => {
      await new SubscriptionCards(page).selectTariffByName(SubscriptionTierTitle.ExtraSmall);

      const buySubscriptionModal = new SubscriptionBuyModal(page);

      // select USDT account
      await selectPaymentMethod(page, TCurrencyAccountSelect.USDT);

      // check that there error displayed
      const accountSelect = new AccountSelect(page);
      await expect(accountSelect.error, 'Account selection error').toBeVisible();

      // click top up account balance (opens deposit modal)
      await expect(buySubscriptionModal.topUpAccountBalanceBtn).toBeVisible();
      await buySubscriptionModal.topUpAccountBalanceBtn.click();

      const address = await new DepositModal(page).getCryptoDepositAddress();
      expect(address.length).toBeGreaterThan(0);
      await closeTopModal(page);
      await paymentCreate(address, '10.11');

      // wait for balance update (10sec)
      // eslint-disable-next-line playwright/no-wait-for-timeout
      await page.waitForTimeout(Settings.timeouts.small);
      await expect(accountSelect.selectedOptionBalance).toBeVisible();
      await expect(accountSelect.selectedOptionBalance).toContainText('10.11');

      // check that error is gone
      await expect(accountSelect.error).toBeHidden();

      // click connect tariff
      await buySubscriptionModal.connectTariffBtn.click();
      // click - pick up a card
      await buySubscriptionModal.purchaseCompletePickCardNowButton.click();
      // verify that create card page is opened
      await expect(page).toHaveURL(/.*app\/create.*/);
    });

    await test.step('Fails to issue card, due to lack of funds', async () => {
      // opens /app/create-card and welcome verification step
      const { firstName, lastName, birthDate } = testUser;
      await performWelcomeVerification(firstName, lastName, birthDate, CountrySetOne.AL, page);

      // select card type step
      const selectCardTariffStep = new SelectCardTariffStep(page);
      await selectCardTariffStep.selectTariff(CardType.Advertisement);

      // select bin step
      const selectCardBinStep = new SelectCardBinStep(page);
      await selectCardBinStep.selectBinByIndex(0);

      // see error, close create card modal
      const selectPaymentMethod = new AccountSelect(page);
      await expect(selectPaymentMethod.error).toBeVisible();
      const step = new DefaultCardIssueStep(page);
      await expect(step.submitButton).toBeDisabled();

      // close issue card modal
      await closeTopModal(page);
      await expect(page).toHaveURL(/.*dashboard.*/);
    });

    const dashboard = new DashboardPage(page);

    await test.step('User top-up account by 20 USDT', async () => {
      // deposit
      // TODO: this is potentialy a command
      await dashboard.accountCards.clickTopUpButton();

      const depositModal = new DepositModal(page);
      const address = await depositModal.getCryptoDepositAddress();
      expect(address).not.toBeNull();
      await paymentCreate(address, '20');
      await closeTopModal(page);
    });

    // check account balance
    await test.step('Check account balance', async () => {
      await page.reload();
      dashboard.closeSidebar();
      await waitForNetworkIdle(page);
      const usdtBalance = await dashboard.accountCards.getAccountBalanceInUSD(
        TCurrencyAccountSelect.USDT
      );
      expect(usdtBalance).toBeCloseTo(15, 0);
    });

    // issue advertisement card
    await test.step('Issue advertisement card', async () => {
      // click issue adv card
      const selectCardType = new SelectCardTypeStep(page);
      await selectCardType.selectCardType('advertisement');

      // select card tariff
      const selectCardTariffStep = new SelectCardTariffStep(page);
      await selectCardTariffStep.selectTariff(CardType.Advertisement);

      // select bin
      const selectCardBinStep = new SelectCardBinStep(page);
      await selectCardBinStep.selectBinByIndex(0);

      // card params step
      const step = new DefaultCardIssueStep(page);
      await step.startBalanceInput.fill('13.86');
      await step.checkTermsAndConditions();
      await step.clickSubmit();

      // card approve step
      const approveStep = new CardApproveStep(page);
      await expect(approveStep.confirmButton).toBeVisible();
      await approveStep.confirmButton.click();

      // continue
      const createCardPage = new CreateCardPage(page);
      await createCardPage.clickContinue();

      await expect(createCardPage.enabledAutoRefillElement).toBeVisible();

      await createCardPage.clickConfirm();
      await expect(page).toHaveURL(/.*dashboard.*/);

      // check card on dashboard
      await expect(page).toHaveURL(/.*dashboard.*/);
      const cards = dashboard.page.getByTestId(DashboardPage.CARD_ITEM_ID);
      await expect(cards).toHaveCount(1);

      const card = cards.first();
      const autoRefillIcon = card.getByTestId(DashboardPage.CARD_AUTO_REFILL_ICON_ID);
      await expect(autoRefillIcon).toHaveClass(/text-fg-green/);
    });

    // Hit the KYC limit (17 - 21 steps)
    await test.step('Hit verification (the KYC) limit, issuing Facebook card', async () => {
      // TODO: this is a bug, we should be able to issue another card without reloading
      await page.reload();
      await waitForNetworkIdle(page);
      // await dashboard.goto();
      await dashboard.newCardButton.click();
      // select card tariff step
      await new SelectCardTariffStep(page).selectTariff(CardType.Facebook);

      // select bin step
      const selectCardBinStep = new SelectCardBinStep(page);
      await selectCardBinStep.selectBinByIndex(0);

      // expect to see KYC dialog
      const kycDialog = new KYCDialog(page);
      await expect(kycDialog.widget).toBeVisible();
      await kycDialog.confirmButton.click();

      // verify /app/settings/verification
      await expect(page).toHaveURL(/.*app\/settings\/verification.*/);

      const verificationPage = new VerificationPage(page);

      const currentVerificationName =
        await verificationPage.availableVerificationScreen.getCurrentVerificationName();
      expect(currentVerificationName?.toLowerCase()).toContain('welcome');
      await verificationPage.availableVerificationScreen.clickUpgradeVerification();

      await performCountrySelect(CountrySetOne.AL, page, true);
      const iframe = new SumsubVerificationScreen(page).getSumsubIframe();
      await expect(iframe.locator('body')).toBeVisible({ timeout: Settings.timeouts.long });
      await performSumsubScaleVerification(page);

      await expect(verificationPage.verificationApproved).toBeVisible({
        timeout: Settings.timeouts.medium,
      });
    });

    // Hit the subscription limit
    await test.step('Hit the subscription limit, issuing Facebook card', async () => {
      // TODO: we need to update test steps, toping up account is not in description
      // we dont have any many so we topup account
      const token = await getUserToken(page);
      const address = await apiUserUSDTAddress(token);
      await paymentCreate(address);

      // create card
      const dashboardPage = new DashboardPage(page);
      await dashboardPage.goto();
      await waitForNetworkIdle(page);
      await dashboardPage.newCardButton.click();

      // select card tariff step
      await new SelectCardTariffStep(page).selectTariff(CardType.Facebook);

      // select bin step
      const selectCardBinStep = new SelectCardBinStep(page);
      await selectCardBinStep.selectBinByIndex(0);

      // now we get 'You reached your card number limit'
      const cardsLimitDialog = new CardLimitDialog(page);
      await expect(cardsLimitDialog.widget).toBeVisible();
      await cardsLimitDialog.raiseLimitButton.click();

      // opened extension sidebar, select 1 card extension Pay 2 Go
      const userTariffExtensionTabsSelect = new UserTariffExtensionTabsSelect(page);
      await expect(userTariffExtensionTabsSelect.widget).toBeVisible();
      const pay2goTab = userTariffExtensionTabsSelect.getTabByName(
        SubscriptionExtensionTitle.Pay2Go
      );
      await pay2goTab.click();

      await page.getByTestId(UserTariffExtensionTabsSelect.PURCHASE_BUTTON_ID).click();
      await page.getByTestId(UserTariffExtensionTabsSelect.DONE_BUTTON_ID).click();

      await selectCardBinStep.selectBinByIndex(0);

      // card params step
      const step = new DefaultCardIssueStep(page);
      await step.startBalanceInput.fill('13.86');
      await step.checkTermsAndConditions();
      await step.clickSubmit();

      // card approve step
      const cardApproveStep = new CardApproveStep(page);
      await expect(cardApproveStep.confirmButton).toBeVisible();
      await cardApproveStep.confirmButton.click();

      const createCardPage = new CreateCardPage(page);
      await createCardPage.clickContinue();
      await createCardPage.clickConfirm();

      await expect(page).toHaveURL(/.*dashboard.*/);
    });

    await test.step('Hit the subscription limit again, issuing Facebook card', async () => {
      // create card
      const dashboardPage = new DashboardPage(page);
      await dashboardPage.goto();
      await waitForNetworkIdle(page);

      await dashboardPage.newCardButton.click();
      await new SelectCardTariffStep(page).selectTariff(CardType.Facebook);
      const selectCardBinStep = new SelectCardBinStep(page);
      await selectCardBinStep.selectBinByIndex(0);

      // now we get 'You reached your card number limit'
      const cardsLimitDialog = new CardLimitDialog(page);
      await expect(cardsLimitDialog.widget).toBeVisible();
    });

    await test.step('Upgrade subscription to Small', async () => {
      // go to detail page
      const subscriptionDetailPage = new SubscriptionDetailPage(page);
      await subscriptionDetailPage.goto();
      await waitForNetworkIdle(page);

      // click upgrade panel button
      await subscriptionDetailPage.upgradePlanPanelButton.click();

      // select small tariff
      const cardsWidget = new SubscriptionCards(page);
      await cardsWidget.selectTariffByName(SubscriptionTierTitle.Small);
      await subscriptionDetailPage.upgradeConnectButton.click();
      await subscriptionDetailPage.modalSuccessButton.click();

      await subscriptionDetailPage.goto();
      await waitForNetworkIdle(page);

      await expect(subscriptionDetailPage.currentTariffTitle).toHaveText(
        SubscriptionTierTitle.Small
      );
    });

    await test.step('Issue ultima card successfully', async () => {
      const dashboardPage = new DashboardPage(page);
      // go to create card page
      const createCardPage = new CreateCardPage(page);
      await createCardPage.goto();
      waitForNetworkIdle(page);

      // select tariff
      await createCardPage.selectCardTariffStep.selectTariff(CardType.Ultima);

      // card params step
      await createCardPage.ultimaWithoutSubscriptionCardIssueStep.checkAgreement();
      await createCardPage.ultimaWithoutSubscriptionCardIssueStep.clickContinue();

      // card approve step
      await createCardPage.cardApproveStep.confirmButton.click();

      // success
      await createCardPage.clickContinue();
      await createCardPage.clickConfirm();

      await expect(page).toHaveURL(/.*dashboard.*/);
      const cards = dashboardPage.page.getByTestId(DashboardPage.CARD_ITEM_ID);
      await expect(cards).toHaveCount(3);
    });

    // issue 2 advertisement cards
    const moreAdvertisementCards = [1, 2];
    for (const card of moreAdvertisementCards) {
      const createCardPage = new CreateCardPage(page);
      await test.step(`Issue advertisement card ${card}`, async () => {
        // go to create card page
        await createCardPage.goto();

        // select tariff
        await new SelectCardTariffStep(page).selectTariff(CardType.Advertisement);

        // select bin
        const selectCardBinStep = new SelectCardBinStep(page);
        await selectCardBinStep.selectBinByIndex(0);

        // card params step
        const step = new DefaultCardIssueStep(page);
        await step.checkTermsAndConditions();
        await step.clickSubmit();

        // card approve step
        const approveStep = new CardApproveStep(page);
        await approveStep.confirmButton.click();

        // success
        await createCardPage.clickContinue();
        await createCardPage.clickConfirm();

        await expect(page).toHaveURL(/.*dashboard.*/);
      });
    }

    // hit verification limit
    await test.step('Hit verification limit', async () => {
      // create card
      const dashboardPage = new DashboardPage(page);
      await dashboardPage.goto();
      await waitForNetworkIdle(page);
      await dashboardPage.newCardButton.click();
      await waitForNetworkIdle(page);

      const createCardPage = new CreateCardPage(page);
      await expect(createCardPage.selectCardTypeStep.container).toBeVisible();

      await expect(page).toHaveURL(/.*create.*/);

      await createCardPage.selectCardTariffStep.selectTariff(CardType.Ultima);

      await createCardPage.ultimaWithoutSubscriptionCardIssueStep.checkAgreement();
      await createCardPage.ultimaWithoutSubscriptionCardIssueStep.clickContinue();

      // see KYC dialog
      const kycDialog = new KYCDialog(page);
      await expect(kycDialog.widget).toBeVisible();
    });
  }
);
