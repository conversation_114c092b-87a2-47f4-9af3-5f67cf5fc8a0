import {
  CountrySetOne,
  CountrySetThree,
  performCountrySelect,
} from '../../support/reusable/commands/verification/country-select';
import { performWelcomeVerification } from '../../support/reusable/commands/verification/welcome-verification';
import { WelcomeVerificationScreen } from '../../support/pages/widgets/WelcomeVerificationScreen';
import { VerificationPage } from '../../support/pages/VerificationPage';
import { testUser } from '../../support/helpers/random-helpers';
import { test } from '../../support/fixtures/user.fixture';
import { expect } from '@playwright/test';

test.describe('Welcome verification', { tag: ['@verification'] }, () => {
  test.describe('Welcome', () => {
    test('Success', async ({ loggedInUser }) => {
      const { page } = loggedInUser;

      await test.step('Open verification', async () => {
        const verificationPage = new VerificationPage(page);
        await verificationPage.goto();
      });

      await test.step('Select welcome verification', async () => {
        const verificationPage = new VerificationPage(page);
        await verificationPage.availableVerificationScreen.clickUpgradeVerification();
      });

      await test.step('Complete welcome verification', async () => {
        const { firstName, lastName, birthDate } = testUser;
        await performWelcomeVerification(firstName, lastName, birthDate, CountrySetOne.AL, page);
        const verificationPage = new VerificationPage(page);
        await expect(verificationPage.verificationApproved).toBeVisible();
      });

      await test.step('Check verification status', async () => {
        const verificationPage = new VerificationPage(page);
        await verificationPage.goto();

        const text = await verificationPage.availableVerificationScreen.getCurrentVerificationName();
        expect(text?.toLowerCase()).toContain('welcome');
      });
    });

    test('should fail - blocked country', async ({ loggedInUser }) => {
      const { page } = loggedInUser;
      const verificationPage = new VerificationPage(page);

      await test.step('Open verification', async () => {
        await verificationPage.goto();
      });

      await test.step('Select Welcome verification', async () => {
        await verificationPage.availableVerificationScreen.clickUpgradeVerification();
      });

      await test.step('Select blocked country', async () => {
        const welcomeStep = new WelcomeVerificationScreen(page);
        await performCountrySelect(CountrySetThree.AF, page);

        await expect(welcomeStep.unsupportedRegionMessage).toBeVisible();
      });
    });
  });
});
