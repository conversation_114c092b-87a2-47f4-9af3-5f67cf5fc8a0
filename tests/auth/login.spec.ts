import { performLogin } from '../../support/reusable/commands/auth/login';
import { apiUserRegister } from '../../support/helpers/backend-api';
import { randomEmail } from '../../support/helpers/random-helpers';
import { LoginPage } from '../../support/pages/LoginPage';
import { expect, test } from '@playwright/test';

test.describe('With registered user', { tag: ['@login', '@auth'] }, () => {
  const newUser = randomEmail();

  test.beforeAll(async () => {
    await apiUserRegister(newUser, newUser);
  });

  test('Should successfully login with correct credentials', async ({ page }) => {
    await performLogin(newUser, newUser, page);
    await expect(page).toHaveURL(/.*dashboard.*/);
  });

  test('Should fail to login - incorrect email', async ({ page }) => {
    const loginPage = new LoginPage(page);
    await loginPage.goto();
    await loginPage.continueWithEmail();
    await loginPage.fillEmail(randomEmail());
    await loginPage.fillPassword(newUser);
    await loginPage.submit();

    const errorContainer = await loginPage.getLoginErrorContainer();
    await expect(errorContainer).toBeVisible();
    const errorMessage = await errorContainer.textContent();
    expect(String(errorMessage).length > 0).toBeTruthy();
  });

  test('Should fail to login - incorrect password', async ({ page }) => {
    const loginPage = new LoginPage(page);
    await loginPage.goto();
    await loginPage.continueWithEmail();
    await loginPage.fillEmail(newUser);
    await loginPage.fillPassword('password1');
    await loginPage.submit();

    const errorContainer = await loginPage.getLoginErrorContainer();
    await expect(errorContainer).toBeVisible();
    const errorMessage = await errorContainer.textContent();
    expect(String(errorMessage).length > 0).toBeTruthy();
  });
});
