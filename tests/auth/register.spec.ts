import { performRegistration } from '../../support/reusable/commands/auth/registration';
import { randomEmail } from '../../support/helpers/random-helpers';
import { RegisterPage } from '../../support/pages/RegisterPage';
import { expect } from '@playwright/test';
import { test } from 'playwright/test';

test.describe('Register', { tag: ['@register', '@auth'] }, () => {
  test('Should successfully register user', async ({ page }) => {
    const newUser = randomEmail();
    await performRegistration(newUser, 'password', page);
    await expect(page).toHaveURL(/.*dashboard.*/);
  });

  test('Should fail to register - user didnt fill form', async ({ page }) => {
    const registerPage = new RegisterPage(page);
    await registerPage.goto();
    await registerPage.continueWithEmail();
    const submitButton = registerPage.submitButton;
    await expect(submitButton).toBeDisabled();
  });

  test('Should fail to register - user filled incorrect email', async ({ page }) => {
    const registerPage = new RegisterPage(page);
    await registerPage.goto();
    await registerPage.continueWithEmail();
    await registerPage.fillEmail('QWEQWEQWE');
    await registerPage.fillPassword('password');
    await registerPage.fillConfirmPassword('password');
    await registerPage.checkAgreements();
    const submitButton = registerPage.submitButton;
    await expect(submitButton).toBeDisabled();

    const emailInputError = registerPage.getEmailInputError();
    await expect(emailInputError).toBeVisible();
  });

  test('Should fail to register - user didnt fill password', async ({ page }) => {
    const registerPage = new RegisterPage(page);
    await registerPage.goto();
    await registerPage.continueWithEmail();
    await registerPage.fillEmail(randomEmail());

    const submitButton = registerPage.submitButton;
    await expect(submitButton).toBeDisabled();
  });

  test('Should fail to register - user didnt fill confirm password', async ({ page }) => {
    const registerPage = new RegisterPage(page);
    await registerPage.goto();
    await registerPage.continueWithEmail();
    await registerPage.fillEmail(randomEmail());
    await registerPage.fillPassword('password');

    await expect(registerPage.submitButton).toBeDisabled();
  });

  test('Should fail to register - password and confirm password are not equal', async ({
    page,
  }) => {
    const registerPage = new RegisterPage(page);
    await registerPage.goto();
    await registerPage.continueWithEmail();
    await registerPage.fillEmail(randomEmail());
    await registerPage.fillPassword('password');
    await registerPage.fillConfirmPassword('password1');

    const error = await registerPage.getConfirmPasswordInputError();
    await expect(error).toBeVisible();

    await expect(registerPage.submitButton).toBeDisabled();
  });

  test('Should fail to register - one checkbox is not checked', async ({ page }) => {
    const registerPage = new RegisterPage(page);
    await registerPage.goto();
    await registerPage.continueWithEmail();
    await registerPage.fillEmail(randomEmail());
    await registerPage.fillPassword('password');
    await registerPage.fillConfirmPassword('password');
    await expect(registerPage.submitButton).toBeDisabled();

    await registerPage.countryCheckbox.click();

    await expect(registerPage.submitButton).toBeDisabled();

    await registerPage.privacyPolicyCheckbox.click();

    await expect(registerPage.submitButton).toBeEnabled();
  });
});
