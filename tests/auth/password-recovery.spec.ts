import { apiUserRegister } from '../../support/helpers/backend-api';
import { randomEmail } from '../../support/helpers/random-helpers';
import { LoginPage } from '../../support/pages/LoginPage';
import { expect, test } from '@playwright/test';

test.describe('With registered user', { tag: ['@password-recovery', '@auth'] }, () => {
  const newUser = randomEmail();

  test.beforeAll(async () => {
    await apiUserRegister(newUser, newUser);
  });

  test('Should successfully send password recovery link', async ({ page }) => {
    const loginPage = new LoginPage(page);
    await loginPage.goto();
    await loginPage.continueWithEmail();
    await loginPage.openForgotPasswordModal();
    await loginPage.fillRecoveryEmail(newUser);

    const submitButton = loginPage.sendLinkButton;
    await expect(submitButton).toBeEnabled();
    await submitButton.click();

    await expect(loginPage.sendLinkError).toBeHidden();
    await expect(loginPage.sendLinkSuccess).toBeVisible();
  });

  test('Should fail send password recovery link: user didnt fill form', async ({ page }) => {
    const loginPage = new LoginPage(page);
    await loginPage.goto();
    await loginPage.continueWithEmail();
    await loginPage.openForgotPasswordModal();

    await expect(loginPage.submitButton).toBeDisabled();
  });

  test('Should fail send password recovery link: user filled incorrect email', async ({ page }) => {
    const loginPage = new LoginPage(page);
    await loginPage.goto();
    await loginPage.continueWithEmail();
    await loginPage.openForgotPasswordModal();

    await loginPage.fillRecoveryEmail(randomEmail());

    await expect(loginPage.sendLinkButton).toBeEnabled();
    await loginPage.sendLinkButton.click();
    await expect(loginPage.sendLinkError).toBeVisible();
  });
});
