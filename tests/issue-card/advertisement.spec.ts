import { selectPaymentMethod } from '../../support/reusable/commands/issue-card/steps/selectPaymentMethod';
import { waitForNetworkIdle } from '../../support/helpers/wait-for-network-idle';
import { CreateCardPage } from '../../support/pages/CreateCardPage';
import { expect, test } from '../../support/fixtures/user.fixture';
import { DashboardPage } from '../../support/pages/DashboardPage';
import { Settings } from '../../support/settings';
import { CardType, SubscriptionTierTitle, VerificationTier } from '../../support/types';

test.describe('Issue advertisement card', { tag: ['@issue-card', '@advertisement'] }, () => {
  test.describe('With user with Scale subscription', () => {
    test('Should be able to issue advertisement card', async ({ withSubscription }) => {
      test.setTimeout(Settings.timeouts.extraLong);
      const { page } = await withSubscription(SubscriptionTierTitle.Small, VerificationTier.Scale);
      await expect(page).toHaveURL(/.*dashboard.*/);
      const createCardPage = new CreateCardPage(page);
      await createCardPage.goto();

      await test.step('Select tariff step', async () => {
        const button = createCardPage.selectCardTariffStep.getCardButtonByTitle(
          CardType.Advertisement
        );
        await expect(button).toBeVisible();
        await button.click();
      });

      await test.step('Select BIN step', async () => {
        const { selectBinStep } = createCardPage;
        await selectBinStep.selectBinByIndex(0);
      });

      await test.step('Card params step', async () => {
        await waitForNetworkIdle(page, 7000);
        const { defaultCardIssueStep } = createCardPage;
        await selectPaymentMethod(page);
        await defaultCardIssueStep.checkTermsAndConditions();
        await defaultCardIssueStep.clickSubmit();
      });

      await test.step('Approve card step', async () => {
        await createCardPage.cardApproveStep.confirmButton.click();
      });

      await test.step('Success modals', async () => {
        await createCardPage.clickContinue();
        await createCardPage.clickConfirm();
      });

      await test.step('Verify card in dashboard', async () => {
        await expect(page).toHaveURL(/.*dashboard.*/);
        const dashboardPage = new DashboardPage(page);
        const cards = dashboardPage.page.getByTestId(DashboardPage.CARD_ITEM_ID);
        await expect(cards).toHaveCount(1);
      });
    });
  });
});
