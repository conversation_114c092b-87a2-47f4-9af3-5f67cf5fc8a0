import { UltimaWithoutSubscriptionCardIssueStep } from '../../support/pages/widgets/issue-card/steps/UltimaWithoutSubscriptionCardIssueStep';
import { CardApproveStep } from '../../support/pages/widgets/issue-card/steps/CardApproveStep';
import { haveCorrectTotalCalculated } from '../../support/reusable/shared-examples/issue-card/have-correct-total-calculated';
import { CreateCardPage } from '../../support/pages/CreateCardPage';
import {
  CardType,
  SubscriptionTierTitle,
  TCurrencyAccountSelect,
  VerificationTier,
} from '../../support/types';
import { DashboardPage } from '../../support/pages/DashboardPage';
import { test } from '../../support/fixtures/user.fixture';
import { expect } from '@playwright/test';
import { Settings } from '../../support/settings';
import { randomString, testUser } from '../../support/helpers/random-helpers';
import { performWelcomeVerification } from '../../support/reusable/commands/verification/welcome-verification';
import { createCardPromocodeEmulate, paymentCreate } from '../../support/helpers/emulate-api';
import { waitForNetworkIdle } from '../../support/helpers/wait-for-network-idle';
import { AutoBuyPaymentStep } from '../../support/pages/widgets/issue-card/steps/AutoBuyPaymentStep';
import { performAutobuy } from '../../support/reusable/commands/issue-card/steps/autobuy';
import { CountrySetOne } from '../../support/reusable/commands/verification/country-select';

test.describe('Issue Ultima Card', { tag: ['@issue-card', '@ultima'] }, () => {
  test.describe('With user with scale verification and positive balance', () => {
    test.beforeEach(async ({ withSubscription }) => {
      const { page } = await withSubscription(SubscriptionTierTitle.Small, VerificationTier.Scale);
      const dashboardPage = new DashboardPage(page);
      await dashboardPage.clickIssueUltimaCard();
    });

    test.describe('With different payment systems and card tariffs', () => {
      const paymentSystems = [
        { name: 'Mastercard', value: 0 },
        { name: 'Visa', value: 1 },
      ];

      paymentSystems.forEach(({ name: paymentSystem, value }) => {
        const periods = [
          { name: 'Weekly', period: 0 },
          { name: 'Monthly', period: 1 },
          { name: 'Annually', period: 2 },
        ];

        periods.forEach(({ name, period }) => {
          test(`Should issue card with ${paymentSystem} and with ${name} tariff`, async ({
            page,
          }) => {
            await test.step('Card params', async () => {
              const issueCardStep = new UltimaWithoutSubscriptionCardIssueStep(page);
              await issueCardStep.selectCardTariff(period);
              await issueCardStep.selectPaymentSystem(value);
              await issueCardStep.fillStartBalance('5000');

              // with BTC
              await issueCardStep.selectAccount(TCurrencyAccountSelect.BTC);
              await haveCorrectTotalCalculated(page, TCurrencyAccountSelect.BTC);

              // with USDT
              await issueCardStep.selectAccount(TCurrencyAccountSelect.USDT);
              await haveCorrectTotalCalculated(page, TCurrencyAccountSelect.USDT);

              await issueCardStep.checkAgreement();
              await issueCardStep.clickContinue();
            });

            await test.step('Approve card', async () => {
              const approveStep = new CardApproveStep(page);
              await expect(approveStep.confirmButton).toBeEnabled();
              await approveStep.confirmButton.click();
            });

            await test.step('Confirm issue card', async () => {
              const createCardPage = new CreateCardPage(page);
              await createCardPage.clickContinue();
              await createCardPage.clickConfirm();
            });

            await test.step('Check card in dashboard', async () => {
              const dashboardPage = new DashboardPage(page);
              await expect(page).toHaveURL(/.*dashboard.*/);

              // check card in dashboard
              const cards = dashboardPage.page.getByTestId(DashboardPage.CARD_ITEM_ID);
              await expect(cards).toHaveCount(1);

              const card = cards.first();
              const autoRefillIcon = card.getByTestId(DashboardPage.CARD_AUTO_REFILL_ICON_ID);
              await expect(autoRefillIcon).toHaveClass(/text-fg-green/);
            });
          });
        });
      });
    });



    test.describe('With card promocode', () => {
      test('should be able to issue an ultima card', async ({ withSubscription }) => {
        const { page } = await withSubscription(
          SubscriptionTierTitle.Small,
          VerificationTier.Scale
        );
        await expect(page).toHaveURL(/.*dashboard.*/);
        const createCardPage = new CreateCardPage(page);
        await createCardPage.goto();

        // create unique promocode
        const code = `testpromo${randomString(4)}`;
        const paymentDiscountPercent = 50;
        const startingBalanceBonus = 15;
        await createCardPromocodeEmulate(code, paymentDiscountPercent, startingBalanceBonus);

        await test.step('Select card tariff step', async () => {
          await createCardPage.selectCardTariffStep.selectTariff(CardType.Ultima);
        });

        await test.step('Card params step', async () => {
          const { defaultCardIssueStep } = createCardPage;
          const promocodeWidget = defaultCardIssueStep.promocode;
          await waitForNetworkIdle(page);

          await promocodeWidget.applyPromocode(code);

          const summary = defaultCardIssueStep.summary;

          await expect(summary.monthlyPaymentDiscountElement).toContainText(
            paymentDiscountPercent.toString()
          );
          await expect(summary.startingBalanceBonusElement).toContainText(
            startingBalanceBonus.toString()
          );

          await defaultCardIssueStep.checkTermsAndConditions();
          await defaultCardIssueStep.clickSubmit();
        });

        await test.step('Card approve step', async () => {
          await createCardPage.cardApproveStep.confirmButton.click();
        });

        await test.step('Success', async () => {
          await createCardPage.clickContinue();
          await createCardPage.clickConfirm();
        });

        await test.step('Verify card in dashboard', async () => {
          await expect(page).toHaveURL(/.*dashboard.*/);
          const dashboardPage = new DashboardPage(page);
          const cards = dashboardPage.page.getByTestId(DashboardPage.CARD_ITEM_ID);
          await expect(cards).toHaveCount(1);
        });
      });
    });
  });
  test.describe('With fresh user', () => {
    test.setTimeout(Settings.timeouts.long);
    const { lastName, firstName, birthDate } = testUser;

    test.describe('With different payment systems and card tariffs', () => {
      const paymentSystems = [
        { system: 'Mastercard', value: 0 },
        { system: 'Visa', value: 1 },
      ];

      paymentSystems.forEach(({ system, value }) => {
        const periods = [
          { name: 'Weekly', period: 0 },
          { name: 'Monthly', period: 1 },
          { name: 'Annually', period: 2 },
        ];
        periods.forEach(({ name, period }) => {
          test(`Should issue card with ${name} tariff and ${system} payment system`, async ({
            loggedInUser,
          }) => {
            const { page } = loggedInUser;
            await expect(page).toHaveURL(/.*dashboard.*/);
            const dashboardPage = new DashboardPage(page);
            let createCardPage = new CreateCardPage(page);

            await test.step('Click issue card', async () => {
              await expect(dashboardPage.issueUltimaCardButton).toBeVisible();
              await expect(dashboardPage.issueUltimaCardButton).toBeEnabled();
              await dashboardPage.issueUltimaCardButton.click();
            });

            await test.step('Complete welcome verification', async () => {
              await performWelcomeVerification(
                firstName,
                lastName,
                birthDate,
                CountrySetOne.AL,
                page
              );
            });

            await test.step('Card issue step', async () => {
              const issueCardStep = new UltimaWithoutSubscriptionCardIssueStep(page);
              await issueCardStep.selectCardTariff(period);
              await issueCardStep.selectPaymentSystem(value);

              await haveCorrectTotalCalculated(page);

              await issueCardStep.checkAgreement();
              await issueCardStep.clickContinue();
            });

            await test.step('Complete autobuy step', async () => {
              await performAutobuy(page);
            });

            await test.step('Complete card issue', async () => {
              await createCardPage.clickContinue();
              await createCardPage.clickConfirm();

              await expect(page).toHaveURL(/.*dashboard.*/);
            });
          });
        });
      });
    });

    test.describe('Variations', () => {
      test.setTimeout(Settings.timeouts.fiveMinutes / 2);
      test.beforeEach(async ({ loggedInUser }) => {
        const { page } = loggedInUser;
        await expect(page).toHaveURL(/.*dashboard.*/);
        const issueCardButton = page.getByTestId('issue-ultima-card-button');
        await issueCardButton.click();
        await performWelcomeVerification(firstName, lastName, birthDate, CountrySetOne.AL, page);
      });

      // starting balance buttons
      test('starting balance buttons', async ({ page }) => {
        const issueCardPage = new UltimaWithoutSubscriptionCardIssueStep(page);

        for (const { order, value } of [
          { order: 0, value: '50.00' },
          { order: 1, value: '100.00' },
          { order: 2, value: '250.00' },
          { order: 3, value: '500.00' },
        ]) {
          await issueCardPage.clickStartBalance(order);
          const startingBalanceInput = await issueCardPage.getStartingBalanceInput();
          await expect(startingBalanceInput).toHaveValue(value);
        }
      });

      // without agreement checkbox
      test('With not checked agreement checkbox: it should show red label', async ({
        loggedInUser,
      }) => {
        const { page } = loggedInUser;
        const issueCardStep = new UltimaWithoutSubscriptionCardIssueStep(page);

        await issueCardStep.clickContinue();
        const agreementCheckbox = issueCardStep.page.locator(
          UltimaWithoutSubscriptionCardIssueStep.AGREEMENT_CHECKBOX
        );
        const agreementCheckboxLabel = agreementCheckbox.locator('+ *');

        // assert text color is red
        await expect(agreementCheckboxLabel).toHaveCSS('color', 'rgb(255, 71, 71)');
      });

      //TODO: refactor later, extract and get from API
      const welcomeTransferLimit = 500;
      test('With transfer more than requested, but less than welcome limit. It should issue card.', async ({
        loggedInUser,
      }) => {
        const { page } = loggedInUser;
        const issueCardPage = new UltimaWithoutSubscriptionCardIssueStep(page);

        await issueCardPage.clickStartBalance(1);

        await haveCorrectTotalCalculated(page);

        await issueCardPage.checkAgreement();
        await issueCardPage.clickContinue();

        // autobuy
        const autoBuyStep = new AutoBuyPaymentStep(page);
        const address = await autoBuyStep.getWalletAddress();

        await paymentCreate(address, String(welcomeTransferLimit - 1));

        const createCardPage = new CreateCardPage(page);

        await createCardPage.clickContinue();
        await createCardPage.clickConfirm();

        await expect(page).toHaveURL(/.*dashboard.*/);

        const dashboardPage = new DashboardPage(page);
        const cards = dashboardPage.page.getByTestId(DashboardPage.CARD_ITEM_ID);
        await expect(cards).toHaveCount(1);
      });

      // with transfer more than requested and more than welcome limit
      test('With transfer more than requested and more than welcome limit. It should not issue card.', async ({
        loggedInUser,
      }) => {
        const { page } = loggedInUser;
        const issueCardPage = new UltimaWithoutSubscriptionCardIssueStep(page);

        await issueCardPage.clickStartBalance(1);
        await issueCardPage.checkAgreement();
        await issueCardPage.clickContinue();

        // autobuy
        const autoBuyStep = new AutoBuyPaymentStep(page);
        const address = await autoBuyStep.getWalletAddress();

        await paymentCreate(address, String(welcomeTransferLimit * 2));

        const createCardPage = new CreateCardPage(page);
        await createCardPage.clickContinue();

        await waitForNetworkIdle(page);

        await expect(page).toHaveURL(/.*dashboard.*/);
        const dashboardPage = new DashboardPage(page);

        const cards = dashboardPage.page.getByTestId(DashboardPage.CARD_ITEM_ID);
        await expect(cards).toHaveCount(0);
      });

      // with transfer less than requested
      test('Transfer less than requested', async ({ loggedInUser }) => {
        const { page } = loggedInUser;
        const issueCardPage = new UltimaWithoutSubscriptionCardIssueStep(page);

        await issueCardPage.clickStartBalance(1);
        await issueCardPage.checkAgreement();
        await issueCardPage.clickContinue();

        // autobuy
        const autoBuyStep = new AutoBuyPaymentStep(page);
        const address = await autoBuyStep.getWalletAddress();
        const amount = await autoBuyStep.getTotalTopUp();
        const transferAmount = String(Number(amount) / 2);

        await paymentCreate(address, transferAmount);

        const createCardPage = new CreateCardPage(page);
        await createCardPage.clickContinue();
        await createCardPage.clickConfirm();

        await expect(page).toHaveURL(/.*dashboard.*/);
        const dashboardPage = new DashboardPage(page);
        const cards = dashboardPage.page.getByTestId(DashboardPage.CARD_ITEM_ID);
        await expect(cards).toHaveCount(1);
      });
    });
  });
});
