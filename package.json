{"name": "dashboard-autotest", "type": "module", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "npx playwright test", "test:ui": "npx playwright test --ui", "test:debug": "playwright test --headed --debug", "test:smoke": "npx playwright test --project=chrome-desktop --grep @smoke", "report": "npx playwright show-report --port=9323", "lint": "npx eslint .", "type-check": "tsc --noEmit"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@eslint/js": "^9.34.0", "@playwright/test": "^1.54.2", "@types/node": "^24.3.0", "@typescript-eslint/eslint-plugin": "^8.41.0", "@typescript-eslint/parser": "^8.41.0", "allure-playwright": "^3.4.1", "ortoni-report": "^4.0.1", "eslint": "^9.34.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-playwright": "^2.2.2", "prettier": "^3.6.2", "typescript": "^5.9.2"}, "dependencies": {"dotenv": "^17.2.1"}, "engines": {"node": ">=22.18.0"}}