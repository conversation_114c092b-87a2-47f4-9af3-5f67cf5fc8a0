job("e2e") {
  startOn {
    schedule { cron("0 8 * * *") }
  }
  container(image = "mcr.microsoft.com/playwright:v1.54.2-noble") {
    requirements {
      workerTags("docker")
    }
    env["CI"] = "true"
    shellScript {
      content = """
        export CI=true
        export EMULATE_API_URL=https://emulate.pstage.net/api
        export BASE_URL=https://vue3.pstage.net
        npm ci
        npx playwright test --reporter=line,html
      """.trimIndent()
    }
    fileArtifacts {
        repository = FileRepository(name = "e2e", remoteBasePath = "{{ run:number }}")
    	localPath = "test-results"
    	remotePath = "test-results.tar.gz"
    	archive = true
      	onStatus = OnStatus.ALWAYS
	}
  }
}
